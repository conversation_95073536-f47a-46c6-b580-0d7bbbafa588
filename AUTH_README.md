# Authentication System

This Vue 3 frontend provides a complete authentication system that connects to your NestJS backend.

## Features

- **User Registration** - Sign up with name, email, and password
- **User Login** - Authenticate with email and password
- **Forgot Password** - Request password reset via email
- **Reset Password** - Set new password using reset token
- **Auto Token Refresh** - Automatically refresh expired access tokens
- **Route Guards** - Protect routes based on authentication status

## Components Created

### Views
- `Login.vue` - User login form
- `Signup.vue` - User registration form
- `ForgotPassword.vue` - Request password reset
- `ResetPassword.vue` - Set new password with token

### Store
- `auth.ts` - Pinia store for authentication state management

### Utils
- `axios.ts` - Configured axios instance with interceptors
- `guards.ts` - Route guards for authentication

## API Endpoints Used

- `POST /user/signup` - Register new user
- `POST /user/login` - Authenticate user
- `POST /user/forgot-password` - Request password reset
- `POST /user/reset-password` - Reset password with token
- `POST /user/refresh-token` - Refresh access token

## Usage

1. **Start the backend server** (NestJS on port 3000)
2. **Start the frontend** with `npm run dev`
3. **Navigate to:**
   - `/signup` - Create new account
   - `/login` - Sign in
   - `/forgot-password` - Reset password

## Configuration

Update the API base URL in `src/utils/axios.ts` if your backend runs on a different port:

```typescript
const API_BASE = 'http://localhost:3000' // Change this if needed
```

## Authentication Flow

1. User signs up or logs in
2. Backend returns access token and refresh token
3. Tokens are stored in localStorage
4. Access token is automatically added to API requests
5. When access token expires, refresh token is used automatically
6. If refresh fails, user is redirected to login

## Security Features

- Passwords must be at least 6 characters with at least one number
- Tokens are automatically refreshed
- Route guards prevent unauthorized access
- Sensitive routes redirect unauthenticated users

The system is now ready to use with your existing NestJS backend!

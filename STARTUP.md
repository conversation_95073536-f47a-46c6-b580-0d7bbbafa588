# Quick Start Guide

## 1. Start Backend (NestJS)
```bash
cd /Users/<USER>/Desktop/starter-dough-nest
npm run start:dev
```
You should see: `🚀 Server running on http://localhost:3000`

## 2. Start Frontend (Vue)
```bash
cd /Users/<USER>/Desktop/starterdough-vue-frontend  
npm run dev
```
You should see: `Local: http://localhost:5173/`

## 3. Test Connection
1. Open http://localhost:5173
2. Click "Test Connection" button
3. Should show "✅ Backend connected successfully!"

## 4. Test Authentication
1. Click "Sign Up" to create account
2. Fill form and submit
3. Go to "Login" and sign in
4. Should redirect to home with "You are successfully logged in!"

## Troubleshooting
- Make sure PostgreSQL is running
- Check backend console for errors
- Check browser console for CORS errors
- Verify both servers are running on correct ports

import { defineS<PERSON> } from 'pinia'
import api from '../utils/axios'

interface Profile {
  custom_avatar_url?: string
  avatar_config?: {
    gradient: string
    face: string
  }
}

interface User {
  id: number
  name: string
  email: string
  profile?: Profile
}

interface AuthState {
  user: User | null
  isAuthenticated: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    isAuthenticated: false
  }),

  actions: {
    async checkAuth() {
      try {
        const response = await api.get('/user/me')
        this.user = response.data.user
        this.isAuthenticated = true
        return true
      } catch (error) {
        this.user = null
        this.isAuthenticated = false
        return false
      }
    },

    async signup(name: string, email: string, password: string) {
      const response = await api.post('/user/signup', {
        name,
        email,
        password
      })
      return response.data
    },

    async verifyOtp(email: string, otpCode: string) {
      const response = await api.post('/user/verify-otp', {
        email,
        otp: otpCode
      })

      // After successful verification, get user data
      if (response.data.success) {
        await this.checkAuth()
      }

      return response.data
    },

    async resendOtp(email: string) {
      const response = await api.post('/user/resend-otp', {
        email
      })
      return response.data
    },

    async login(email: string, password: string) {
      const response = await api.post('/user/login', {
        email,
        password
      })

      // After successful login, get user data
      if (response.data.success) {
        await this.checkAuth()
      }

      return response.data
    },

    async forgotPassword(email: string) {
      const response = await api.post('/user/forgot-password', {
        email
      })
      return response.data
    },

    async resetPassword(resetToken: string, newPassword: string) {
      const response = await api.post('/user/reset-password', {
        resetToken,
        newPassword
      })
      return response.data
    },

    async resetPasswordWithOtp(email: string, otp: string, newPassword: string, confirmPassword: string) {
      const response = await api.post('/user/reset-password-otp', {
        email,
        otp,
        newPassword,
        confirmPassword
      })
      return response.data
    },

    async logout() {
      try {
        await api.post('/user/logout')
      } catch (error) {
        // Continue with logout even if server request fails
      }

      this.user = null
      this.isAuthenticated = false
    }
  }
})

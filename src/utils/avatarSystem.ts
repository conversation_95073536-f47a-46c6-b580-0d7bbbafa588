// Avatar Design System
// Generates unique profile avatars based on user ID

export interface AvatarConfig {
  gradient: string
  face: string
}

// Gradient definitions with mesh-like radial gradients
export const gradientStyles = {
  sunset: {
    background: `
      radial-gradient(circle at 20% 20%, #ff6b6b 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, #ffa726 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, #ff8a65 0%, transparent 50%),
      linear-gradient(135deg, #ff7043 0%, #ffab40 100%)
    `
  },
  ocean: {
    background: `
      radial-gradient(circle at 20% 20%, #42a5f5 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, #26c6da 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, #66bb6a 0%, transparent 50%),
      linear-gradient(135deg, #29b6f6 0%, #4db6ac 100%)
    `
  },
  purple: {
    background: `
      radial-gradient(circle at 20% 20%, #4db6ac 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, #ec407a 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, #ab47bc 0%, transparent 50%),
      linear-gradient(135deg, #26a69a 0%, #9c27b0 100%)
    `
  },
  peachy: {
    background: `
      radial-gradient(circle at 20% 20%, #fff3e0 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, #ffab91 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, #f8bbd9 0%, transparent 50%),
      linear-gradient(135deg, #ffcc80 0%, #f48fb1 100%)
    `
  },
  minty: {
    background: `
      radial-gradient(circle at 20% 20%, #a5d6a7 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, #fff59d 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, #80deea 0%, transparent 50%),
      linear-gradient(135deg, #81c784 0%, #4dd0e1 100%)
    `
  },
  lavender: {
    background: `
      radial-gradient(circle at 20% 20%, #f8bbd9 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, #fff3e0 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, #d1c4e9 0%, transparent 50%),
      linear-gradient(135deg, #f48fb1 0%, #b39ddb 100%)
    `
  },
  candy: {
    background: `
      radial-gradient(circle at 20% 20%, #ff8a80 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, #f48fb1 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, #ea80fc 0%, transparent 50%),
      linear-gradient(135deg, #ff5722 0%, #e91e63 100%)
    `
  }
}

export const gradientNames = Object.keys(gradientStyles) as Array<keyof typeof gradientStyles>

export const faceTypes = [
  'smiley',
  'wink', 
  'happy',
  'cool',
  'starry',
  'blush',
  'laugh',
  'peaceful'
] as const

export type FaceType = typeof faceTypes[number]
export type GradientType = keyof typeof gradientStyles

// Simple hash function to convert user ID to deterministic number
function hashUserId(userId: number): number {
  let hash = 0
  const str = userId.toString()
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash)
}

// Generate avatar configuration based on user ID
export function generateAvatarConfig(userId: number): AvatarConfig {
  const hash = hashUserId(userId)
  
  const gradientIndex = hash % gradientNames.length
  const faceIndex = Math.floor(hash / gradientNames.length) % faceTypes.length
  
  return {
    gradient: gradientNames[gradientIndex],
    face: faceTypes[faceIndex]
  }
}

// Get avatar configuration for user (from profile or generate new)
export function getAvatarConfig(user: any): AvatarConfig {
  // If user has stored avatar config, use it
  if (user?.profile?.avatar_config) {
    return user.profile.avatar_config
  }
  
  // If user has ID, generate consistent avatar
  if (user?.id) {
    return generateAvatarConfig(user.id)
  }
  
  // Default fallback - use a specific gradient for demo
  return {
    gradient: 'purple',
    face: 'smiley'
  }
}

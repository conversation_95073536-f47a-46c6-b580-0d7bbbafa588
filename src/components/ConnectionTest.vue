<template>
  <div class="p-4 border rounded">
    <h3 class="text-lg font-bold mb-2">Backend Connection Test</h3>
    <button 
      @click="testConnection" 
      class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      :disabled="loading"
    >
      {{ loading ? 'Testing...' : 'Test Connection' }}
    </button>
    
    <div v-if="result" class="mt-4">
      <div v-if="result.success" class="text-green-600">
        ✅ Backend connected successfully!
      </div>
      <div v-else class="text-red-600">
        ❌ Connection failed: {{ result.error }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import api from '../utils/axios'

const loading = ref(false)
const result = ref<{success: boolean, error?: string} | null>(null)

const testConnection = async () => {
  loading.value = true
  result.value = null
  
  try {
    // Test basic connection to backend
    const response = await api.get('/')
    result.value = { success: true }
  } catch (error: any) {
    console.error('Connection test failed:', error)
    result.value = { 
      success: false, 
      error: error.message || 'Unknown error' 
    }
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="flex justify-center space-x-2">
    <input
      v-for="(digit, index) in otpDigits"
      :key="index"
      :ref="el => otpInputs[index] = el"
      v-model="otpDigits[index]"
      type="text"
      maxlength="1"
      class="w-12 h-12 text-center text-2xl font-bold border-2 border-gray-300 rounded-lg focus:border-indigo-500 focus:outline-none"
      @input="handleOtpInput(index, $event)"
      @keydown="handleKeyDown(index, $event)"
      @paste="handlePaste"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

const emit = defineEmits<{
  otpChange: [otp: string]
  otpComplete: [otp: string]
}>()

const otpDigits = ref(['', '', '', '', '', ''])
const otpInputs = ref<HTMLInputElement[]>([])

const otpCode = computed(() => otpDigits.value.join(''))

const isComplete = computed(() => 
  otpDigits.value.every(digit => digit !== '')
)

const handleOtpInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/[^0-9]/g, '')
  
  otpDigits.value[index] = value
  
  // Auto-focus next input
  if (value && index < 5) {
    otpInputs.value[index + 1]?.focus()
  }
  
  // Emit events
  emit('otpChange', otpCode.value)
  if (isComplete.value) {
    emit('otpComplete', otpCode.value)
  }
}

const handleKeyDown = (index: number, event: KeyboardEvent) => {
  // Handle backspace
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    otpInputs.value[index - 1]?.focus()
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedData = event.clipboardData?.getData('text') || ''
  const digits = pastedData.replace(/[^0-9]/g, '').slice(0, 6).split('')
  
  digits.forEach((digit, index) => {
    if (index < 6) {
      otpDigits.value[index] = digit
    }
  })
  
  // Focus last filled input
  const lastIndex = Math.min(digits.length - 1, 5)
  otpInputs.value[lastIndex]?.focus()
  
  emit('otpChange', otpCode.value)
  if (isComplete.value) {
    emit('otpComplete', otpCode.value)
  }
}

// Method to clear OTP
const clearOtp = () => {
  otpDigits.value = ['', '', '', '', '', '']
  otpInputs.value[0]?.focus()
}

// Method to focus first input
const focusFirst = () => {
  otpInputs.value[0]?.focus()
}

// Expose methods to parent
defineExpose({
  clearOtp,
  focusFirst
})

onMounted(() => {
  focusFirst()
})
</script>

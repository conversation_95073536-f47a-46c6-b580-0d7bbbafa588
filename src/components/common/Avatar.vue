<template>
  <div 
    :class="[
      'relative rounded-full flex items-center justify-center overflow-hidden',
      sizeClasses
    ]"
    :style="!customAvatar ? gradientStyle : {}"
    :key="avatarKey"
  >
    <!-- Custom Avatar Image -->
    <img 
      v-if="customAvatar" 
      :src="customAvatar" 
      :alt="user?.name || 'Avatar'"
      class="w-full h-full object-cover"
    />
    
    <!-- Generated Avatar (Gradient + Face) -->
    <div v-else class="relative w-full h-full flex items-center justify-center">
      <svg 
        :class="iconSizeClass" 
        viewBox="0 0 24 24" 
        fill="none"
        class="text-gray-800 drop-shadow-sm"
        v-html="faceIcon"
      >
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getAvatarConfig, gradientStyles, type FaceType } from '../../utils/avatarSystem'

interface User {
  id: number
  name: string
  email: string
  google_id?: string
  avatar_url?: string
  profile?: {
    custom_avatar_url?: string
    avatar_config?: {
      gradient: string
      face: string
    }
  }
}

interface Props {
  user?: User | null
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md'
})

// Force re-render when user data changes
const avatarKey = computed(() => {
  return `${props.user?.id || 'guest'}-${props.user?.profile?.custom_avatar_url || 'default'}`
})

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12', 
    lg: 'w-20 h-20',
    xl: 'w-32 h-32'
  }
  return sizes[props.size]
})

const iconSizeClass = computed(() => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-10 h-10', 
    xl: 'w-16 h-16'
  }
  return sizes[props.size]
})

const customAvatar = computed(() => {
  console.log('🔍 Avatar Debug - Full user object:', props.user)
  console.log('🔍 Avatar Debug - Profile:', props.user?.profile)
  console.log('🔍 Avatar Debug - custom_avatar_url:', props.user?.profile?.custom_avatar_url)
  console.log('🔍 Avatar Debug - avatar_url:', props.user?.avatar_url)
  console.log('🔍 Avatar Debug - google_id:', props.user?.google_id)

  // Check for different avatar sources in order of priority
  let avatarUrl = null

  // 1. Custom avatar from profile
  if (props.user?.profile?.custom_avatar_url) {
    avatarUrl = props.user.profile.custom_avatar_url
    console.log('✅ Found profile custom avatar:', avatarUrl)
  }
  // 2. Direct avatar_url field (for Google OAuth users)
  else if (props.user?.avatar_url) {
    avatarUrl = props.user.avatar_url
    console.log('✅ Found direct avatar_url:', avatarUrl)
  }

  if (!avatarUrl) {
    console.log('❌ No custom avatar URL found')
    return null
  }

  // If it's already a full URL (Google avatars), use it directly
  if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
    console.log('✅ Using external avatar URL:', avatarUrl)
    return avatarUrl
  }

  // If it's a Base64 data URL, use it directly
  if (avatarUrl.startsWith('data:image/')) {
    console.log('✅ Using Base64 avatar')
    return avatarUrl
  }

  // Otherwise, construct server URL with cache busting
  const serverUrl = `http://localhost:3000${avatarUrl}?v=${Date.now()}`
  console.log('✅ Using server avatar URL:', serverUrl)
  return serverUrl
})

const avatarConfig = computed(() => {
  return getAvatarConfig(props.user)
})

const gradientStyle = computed(() => {
  const gradient = avatarConfig.value.gradient
  const style = gradientStyles[gradient as keyof typeof gradientStyles]
  return style ? { background: style.background } : {}
})

const faceIcon = computed(() => {
  const face = avatarConfig.value.face as FaceType
  
  // Face icon components as SVG strings
  const faces = {
    smiley: `
      <circle cx="9" cy="9" r="1.5" fill="currentColor"/>
      <circle cx="15" cy="9" r="1.5" fill="currentColor"/>
      <path d="M8 13s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    `,
    wink: `
      <path d="M7 9l2-1-2-1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <circle cx="15" cy="9" r="1.5" fill="currentColor"/>
      <path d="M8 13s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    `,
    happy: `
      <circle cx="9" cy="9" r="1.5" fill="currentColor"/>
      <circle cx="15" cy="9" r="1.5" fill="currentColor"/>
      <path d="M7 13s2 3 5 3 5-3 5-3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    `,
    cool: `
      <rect x="6" y="8" width="12" height="2" rx="1" fill="currentColor"/>
      <path d="M8 13s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    `,
    starry: `
      <path d="M9 7l1 2 2-1-2 1 1 2-1-2-2 1 2-1-1-2z" fill="currentColor"/>
      <path d="M15 7l1 2 2-1-2 1 1 2-1-2-2 1 2-1-1-2z" fill="currentColor"/>
      <path d="M8 13s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    `,
    blush: `
      <circle cx="9" cy="9" r="1.5" fill="currentColor"/>
      <circle cx="15" cy="9" r="1.5" fill="currentColor"/>
      <circle cx="6" cy="11" r="1" fill="currentColor" opacity="0.6"/>
      <circle cx="18" cy="11" r="1" fill="currentColor" opacity="0.6"/>
      <path d="M8 13s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    `,
    laugh: `
      <path d="M7 8s1-1 2 0 2-1 2 0" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M13 8s1-1 2 0 2-1 2 0" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <ellipse cx="12" cy="14" rx="4" ry="2" fill="currentColor"/>
    `,
    peaceful: `
      <path d="M7 9s1-1 2 0 1 1 0 0" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M15 9s1-1 2 0 1 1 0 0" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      <path d="M10 13h4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    `
  }
  
  return faces[face] || faces.smiley
})
</script>

<template>
  <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Backend Connection Test</h2>
    
    <button 
      @click="testConnection" 
      :disabled="loading"
      class="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200"
    >
      {{ loading ? 'Testing...' : 'Test Connection' }}
    </button>
    
    <div v-if="result" class="mt-4 p-3 rounded-md" :class="resultClass">
      <p class="text-sm font-medium">{{ result }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import api from '../../utils/axios'

const loading = ref(false)
const result = ref('')
const isSuccess = ref(false)

const resultClass = computed(() => {
  return isSuccess.value 
    ? 'bg-green-100 border border-green-400 text-green-700'
    : 'bg-red-100 border border-red-400 text-red-700'
})

const testConnection = async () => {
  loading.value = true
  result.value = ''
  
  try {
    const response = await api.get('/')
    result.value = `✅ Connected! Server says: "${response.data.message || 'Hello World'}"`
    isSuccess.value = true
  } catch (error: any) {
    result.value = `❌ Connection failed: ${error.message}`
    isSuccess.value = false
  } finally {
    loading.value = false
  }
}
</script>

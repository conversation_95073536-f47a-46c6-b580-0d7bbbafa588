<template>
  <div>
    <nav class="bg-gray-400 py-4 px-10 shadow-sm">
      <div class="mx-auto flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <router-link to="/" class="text-white hover:text-purple-100 font-medium">Home</router-link>
          <router-link to="/about" class="text-white hover:text-purple-100 font-medium">About</router-link>
        </div>
        
        <div class="flex items-center space-x-4">
          <template v-if="authStore.isAuthenticated">
            <!-- Avatar Dropdown -->
            <div class="relative" ref="dropdownRef">
              <button 
                @click="toggleDropdown"
                class="flex items-center space-x-2 focus:outline-none hover:opacity-80 transition-opacity"
              >
                <Avatar :user="authStore.user" size="sm" />
              </button>
              
              <!-- Dropdown Menu -->
              <transition name="fade-scale" appear>
                <div
                  v-if="showDropdown"
                  class="absolute right-2 mt-1.5 w-56 bg-white rounded-xl shadow-lg border border-gray-200 z-50"
                >
                  <!-- Profile Section -->
                  <div class="px-3 py-2 border-b border-gray-100">
                    <div class="flex items-center space-x-3">
                     <Avatar :user="authStore.user" size="sm" class="!w-[40px] !h-[40px]"/>

                      <div class="flex-1 min-w-0">
                        <p class="text-[16px] font-medium text-gray-900 truncate">
                          {{ authStore.user?.name || 'User' }}
                        </p>
                        <p class="text-[12px] text-gray-400 truncate">
                          {{ authStore.user?.email }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- Menu Items -->
                  <div class="p-1">
                    <router-link
                      to="/profile"
                      @click="closeDropdown"
                      class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      View Profile
                    </router-link>
                    <router-link
                      to="/settings"
                      @click="closeDropdown"
                      class="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      Settings
                    </router-link>
                    <button
                      @click="handleLogout"
                      class="flex items-center w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      Sign Out
                    </button>
                  </div>
                </div>
              </transition>
            </div>
          </template>
          <template v-else>
            <!-- <router-link 
              to="/auth/signup" 
              class="text-white hover:text-purple-100 bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg font-medium transition-all"
            >
              Sign Up
            </router-link> -->
            <router-link to="/login" 
            class="text-black text-sm rounded-3xl bg-white px-4 py-2 hover:text-purple-100 hover:bg-gray-600 transition-all ">
              Sign In
            </router-link>
          </template>
        </div>
      </div>
    </nav>
    
    <main class="max-w-7xl mx-auto p-4">
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from './stores/auth'
import { useRouter } from 'vue-router'
import Avatar from './components/common/Avatar.vue'

const authStore = useAuthStore()
const router = useRouter()
const showDropdown = ref(false)
const dropdownRef = ref<HTMLElement>()

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const closeDropdown = () => {
  showDropdown.value = false
}

const handleLogout = async () => {
  await authStore.logout()
  closeDropdown()
  router.push('/login')
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    closeDropdown()
  }
}

onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  // Check authentication status on app load
  await authStore.checkAuth()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: all 0.1s ease-out;
}

.fade-scale-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}

.fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}
</style>
<style scoped>
.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: all 0.1s ease;
}

.fade-scale-enter-from,
.fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(4px);
}

.fade-scale-enter-to,
.fade-scale-leave-from {
  opacity: 1;
  transform: scale(1) translateY(0);
}
</style>
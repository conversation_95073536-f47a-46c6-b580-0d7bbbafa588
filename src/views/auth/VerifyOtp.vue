<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-slate-900 mb-2">Verify your email</h1>
        <p class="text-slate-600">
          We sent a 6-digit code to
          <span class="font-medium text-slate-900">{{ email }}</span>
        </p>
      </div>

      <!-- OTP Form -->
      <div class="bg-white rounded-2xl shadow-xl p-8">
        <form @submit.prevent="handleVerifyOtp" class="space-y-6">
          <!-- OTP Input -->
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-4 text-center">
              Enter verification code
            </label>
            <div class="flex justify-center space-x-3">
              <input
                v-for="(digit, index) in otpDigits"
                :key="index"
                :ref="el => otpInputs[index] = el"
                v-model="otpDigits[index]"
                type="text"
                maxlength="1"
                class="w-12 h-12 text-center text-xl font-bold border-2 border-slate-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                @input="handleOtpInput(index, $event)"
                @keydown="handleKeyDown(index, $event)"
                @paste="handlePaste"
              />
            </div>
          </div>

          <button
            type="submit"
            :disabled="loading || !isOtpComplete"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? 'Verifying...' : 'Verify email' }}
          </button>

          <!-- Resend -->
          <div class="text-center">
            <button
              type="button"
              :disabled="resendLoading || resendCooldown > 0"
              @click="handleResendOtp"
              class="text-blue-600 hover:text-blue-500 font-medium disabled:text-slate-400 disabled:cursor-not-allowed"
            >
              {{ resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend code' }}
            </button>
          </div>

          <!-- Error/Success Messages -->
          <div v-if="error" class="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-sm text-red-600">{{ error }}</p>
          </div>

          <div v-if="success" class="p-3 bg-green-50 border border-green-200 rounded-lg">
            <p class="text-sm text-green-600">{{ success }}</p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const email = ref(route.query.email as string || '')
const otpDigits = ref(['', '', '', '', '', ''])
const otpInputs = ref<HTMLInputElement[]>([])
const loading = ref(false)
const resendLoading = ref(false)
const error = ref('')
const success = ref('')
const resendCooldown = ref(0)

let cooldownInterval: NodeJS.Timeout | null = null

const isOtpComplete = computed(() => 
  otpDigits.value.every(digit => digit !== '')
)

const otpCode = computed(() => 
  otpDigits.value.join('')
)

const handleOtpInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/[^0-9]/g, '')
  
  otpDigits.value[index] = value
  
  if (value && index < 5) {
    otpInputs.value[index + 1]?.focus()
  }
}

const handleKeyDown = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    otpInputs.value[index - 1]?.focus()
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedData = event.clipboardData?.getData('text') || ''
  const digits = pastedData.replace(/[^0-9]/g, '').slice(0, 6).split('')
  
  digits.forEach((digit, index) => {
    if (index < 6) {
      otpDigits.value[index] = digit
    }
  })
  
  const lastIndex = Math.min(digits.length - 1, 5)
  otpInputs.value[lastIndex]?.focus()
}

const handleVerifyOtp = async () => {
  if (!isOtpComplete.value) return
  
  loading.value = true
  error.value = ''
  success.value = ''
  
  try {
    await authStore.verifyOtp(email.value, otpCode.value)
    success.value = 'Email verified successfully!'
    setTimeout(() => {
      router.push('/')
    }, 1500)
  } catch (err: any) {
    const errorMessage = err.response?.data?.message || 'An error occurred. Please try again.'
    error.value = errorMessage
    
    if (errorMessage.includes('expired')) {
      success.value = 'Code expired. Click "Resend code" to get a new one.'
    }
    
    otpDigits.value = ['', '', '', '', '', '']
    otpInputs.value[0]?.focus()
  } finally {
    loading.value = false
  }
}

const handleResendOtp = async () => {
  resendLoading.value = true
  error.value = ''
  
  try {
    const response = await authStore.resendOtp(email.value)
    success.value = 'New code sent!'
    
    const cooldownTime = response.canResendIn || 60
    resendCooldown.value = cooldownTime
    
    cooldownInterval = setInterval(() => {
      resendCooldown.value--
      if (resendCooldown.value <= 0) {
        clearInterval(cooldownInterval!)
      }
    }, 1000)
    
    otpDigits.value = ['', '', '', '', '', '']
    otpInputs.value[0]?.focus()
  } catch (err: any) {
    error.value = err.response?.data?.message || 'An error occurred. Please try again.'
  } finally {
    resendLoading.value = false
  }
}

onMounted(() => {
  if (!email.value) {
    router.push('/auth/signup')
  }
  otpInputs.value[0]?.focus()
})

onUnmounted(() => {
  if (cooldownInterval) {
    clearInterval(cooldownInterval)
  }
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- Error Card -->
      <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
        <!-- Error Icon -->
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </div>

        <!-- Error Message -->
        <h1 class="text-2xl font-bold text-slate-900 mb-2">Invalid or expired link</h1>
        <p class="text-slate-600 mb-8">
          This password reset link is invalid or has expired. Please request a new one.
        </p>

        <!-- Actions -->
        <div class="space-y-3">
          <button
            @click="goToForgotPassword"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          >
            Request new reset link
          </button>
          
          <button
            @click="goToLogin"
            class="w-full bg-slate-100 hover:bg-slate-200 text-slate-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          >
            Back to sign in
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToForgotPassword = () => {
  router.push('/forgot-password')
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Reset Your Password
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Enter the code sent to <span class="font-medium">{{ email }}</span>
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleResetPassword">
        <!-- OTP Input -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Verification Code</label>
          <div class="flex justify-center space-x-2">
            <input
              v-for="(digit, index) in otpDigits"
              :key="index"
              :ref="el => otpInputs[index] = el"
              v-model="otpDigits[index]"
              type="text"
              maxlength="1"
              class="w-12 h-12 text-center text-2xl font-bold border-2 border-gray-300 rounded-lg focus:border-red-500 focus:outline-none"
              @input="handleOtpInput(index, $event)"
              @keydown="handleKeyDown(index, $event)"
              @paste="handlePaste"
            />
          </div>
        </div>

        <!-- Password Fields -->
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">New Password</label>
            <input
              v-model="newPassword"
              type="password"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500"
              placeholder="Enter new password"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700">Confirm Password</label>
            <input
              v-model="confirmPassword"
              type="password"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500"
              placeholder="Confirm new password"
            />
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            {{ loading ? 'Resetting Password...' : 'Reset Password' }}
          </button>
        </div>

        <div class="text-center">
          <button
            type="button"
            :disabled="resendLoading"
            @click="handleResendOtp"
            class="text-sm text-red-600 hover:text-red-500 disabled:text-gray-400"
          >
            {{ resendLoading ? 'Sending...' : 'Resend Code' }}
          </button>
        </div>

        <div v-if="error" class="text-red-600 text-sm text-center">
          {{ error }}
        </div>

        <div v-if="success" class="text-green-600 text-sm text-center">
          {{ success }}
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const email = ref(route.query.email as string || '')
const otpDigits = ref(['', '', '', '', '', ''])
const otpInputs = ref<HTMLInputElement[]>([])
const newPassword = ref('')
const confirmPassword = ref('')
const loading = ref(false)
const resendLoading = ref(false)
const error = ref('')
const success = ref('')

const isOtpComplete = computed(() => 
  otpDigits.value.every(digit => digit !== '')
)

const isPasswordsMatch = computed(() => 
  newPassword.value && confirmPassword.value && newPassword.value === confirmPassword.value
)

const isFormValid = computed(() => 
  isOtpComplete.value && isPasswordsMatch.value && newPassword.value.length >= 6
)

const otpCode = computed(() => 
  otpDigits.value.join('')
)

const handleOtpInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/[^0-9]/g, '')
  
  otpDigits.value[index] = value
  
  if (value && index < 5) {
    otpInputs.value[index + 1]?.focus()
  }
}

const handleKeyDown = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    otpInputs.value[index - 1]?.focus()
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedData = event.clipboardData?.getData('text') || ''
  const digits = pastedData.replace(/[^0-9]/g, '').slice(0, 6).split('')
  
  digits.forEach((digit, index) => {
    if (index < 6) {
      otpDigits.value[index] = digit
    }
  })
}

const handleResetPassword = async () => {
  if (!isFormValid.value) return
  
  loading.value = true
  error.value = ''
  success.value = ''
  
  try {
    await authStore.resetPasswordWithOtp(
      email.value, 
      otpCode.value, 
      newPassword.value, 
      confirmPassword.value
    )
    success.value = 'Password reset successfully!'
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to reset password'
    // Clear OTP on error
    otpDigits.value = ['', '', '', '', '', '']
    otpInputs.value[0]?.focus()
  } finally {
    loading.value = false
  }
}

const handleResendOtp = async () => {
  resendLoading.value = true
  error.value = ''
  
  try {
    await authStore.forgotPassword(email.value)
    success.value = 'New code sent!'
    // Clear OTP inputs
    otpDigits.value = ['', '', '', '', '', '']
    otpInputs.value[0]?.focus()
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to resend code'
  } finally {
    resendLoading.value = false
  }
}

onMounted(() => {
  if (!email.value) {
    router.push('/forgot-password')
  }
  otpInputs.value[0]?.focus()
})
</script>

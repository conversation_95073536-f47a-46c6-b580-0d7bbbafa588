<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Set New Password
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          {{ email ? `Enter your new password for ${email}` : 'Create a strong new password for your account' }}
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleResetPassword">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">New Password</label>
            <input
              v-model="newPassword"
              type="password"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Enter new password (min 6 chars, include number)"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700">Confirm Password</label>
            <input
              v-model="confirmPassword"
              type="password"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Confirm your new password"
            />
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading || newPassword !== confirmPassword || !newPassword"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {{ loading ? 'Resetting Password...' : 'Reset Password' }}
          </button>
        </div>

        <div class="text-center">
          <router-link to="/auth/login" class="text-sm text-indigo-600 hover:text-indigo-500">
            Back to Login
          </router-link>
        </div>

        <div v-if="newPassword !== confirmPassword && confirmPassword" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Passwords do not match
        </div>

        <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {{ error }}
        </div>
        
        <div v-if="success" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {{ success }}
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const email = ref(route.query.email as string || '')
const otp = ref(route.query.otp as string || '')
const resetToken = ref(route.query.token as string || '')
const newPassword = ref('')
const confirmPassword = ref('')
const loading = ref(false)
const error = ref('')
const success = ref('')

onMounted(() => {
  if (!resetToken.value && (!email.value || !otp.value)) {
    error.value = 'Invalid reset session. Please try again.'
    setTimeout(() => {
      router.push('/auth/forgot-password')
    }, 3000)
  }
})

const handleResetPassword = async () => {
  if (newPassword.value !== confirmPassword.value) {
    error.value = 'Passwords do not match'
    return
  }

  if (newPassword.value.length < 6) {
    error.value = 'Password must be at least 6 characters long'
    return
  }

  loading.value = true
  error.value = ''
  success.value = ''
  
  try {
    if (resetToken.value) {
      await authStore.resetPassword(resetToken.value, newPassword.value)
    } else {
      await authStore.resetPasswordWithOtp(email.value, otp.value, newPassword.value, confirmPassword.value)
    }
    
    success.value = 'Password reset successfully! Redirecting to login...'
    setTimeout(() => {
      router.push('/auth/login')
    }, 2000)
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to reset password. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>

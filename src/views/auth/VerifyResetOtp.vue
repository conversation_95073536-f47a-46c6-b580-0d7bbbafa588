<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Verify Reset Code
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          We sent a 6-digit code to <span class="font-medium">{{ email }}</span>
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleVerifyOtp">
        <!-- OTP Input Component -->
        <div>
          <OtpInput 
            ref="otpInputRef"
            @otp-change="otp = $event"
            @otp-complete="handleOtpComplete"
          />
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading || !isOtpComplete"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            {{ loading ? 'Verifying...' : 'Verify Code' }}
          </button>
        </div>

        <!-- Resend OTP -->
        <div class="text-center">
          <button
            type="button"
            :disabled="resendLoading || resendCooldown > 0"
            @click="handleResendOtp"
            class="text-sm text-red-600 hover:text-red-500 disabled:text-gray-400"
          >
            {{ resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend Code' }}
          </button>
        </div>

        <div v-if="error" class="text-red-600 text-sm text-center">
          {{ error }}
        </div>

        <div v-if="success" class="text-green-600 text-sm text-center">
          {{ success }}
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import OtpInput from '../../components/auth/OtpInput.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const email = ref(route.query.email as string || '')
const otp = ref('')
const otpInputRef = ref()
const loading = ref(false)
const resendLoading = ref(false)
const error = ref('')
const success = ref('')
const resendCooldown = ref(0)

let cooldownInterval: NodeJS.Timeout | null = null

const isOtpComplete = computed(() => otp.value.length === 6)

const handleOtpComplete = (otpValue: string) => {
  otp.value = otpValue
}

const handleVerifyOtp = async () => {
  if (!isOtpComplete.value) return
  
  loading.value = true
  error.value = ''
  success.value = ''
  
  try {
    success.value = 'Code verified! Redirecting to reset password...'
    setTimeout(() => {
      router.push({
        path: '/auth/reset-password',
        query: {
          email: email.value,
          otp: otp.value,
          verified: 'true'
        }
      })
    }, 1500)
  } catch (err: any) {
    const errorMessage = err.response?.data?.message || 'An error occurred. Please try again.'
    error.value = errorMessage
    
    // If OTP expired, show resend option prominently
    if (errorMessage.includes('expired')) {
      success.value = 'Code expired. Click "Resend Code" to get a new one.'
    }
    
    otpInputRef.value?.clearOtp()
  } finally {
    loading.value = false
  }
}

const handleResendOtp = async () => {
  resendLoading.value = true
  error.value = ''
  
  try {
    await authStore.forgotPassword(email.value)
    success.value = 'New code sent!'
    
    resendCooldown.value = 60
    cooldownInterval = setInterval(() => {
      resendCooldown.value--
      if (resendCooldown.value <= 0) {
        clearInterval(cooldownInterval!)
      }
    }, 1000)
    
    otpInputRef.value?.clearOtp()
  } catch (err: any) {
    error.value = err.response?.data?.message || 'An error occurred. Please try again.'
  } finally {
    resendLoading.value = false
  }
}

onMounted(() => {
  if (!email.value) {
    router.push('/auth/forgot-password')
  }
})

onUnmounted(() => {
  if (cooldownInterval) {
    clearInterval(cooldownInterval)
  }
})
</script>

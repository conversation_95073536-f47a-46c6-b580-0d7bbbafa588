<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <div class="bg-white rounded-lg shadow-md">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200">
          <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
        </div>

        <!-- Tabs -->
        <div class="border-b border-gray-200">
          <nav class="flex space-x-8 px-6">
            <button
              @click="activeTab = 'account'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm',
                activeTab === 'account' 
                  ? 'border-indigo-500 text-indigo-600' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              Account
            </button>
          </nav>
        </div>

        <!-- Account Tab -->
        <div v-if="activeTab === 'account'" class="p-6">
          <!-- Avatar Section -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Profile Avatar</h3>
            
            <div class="flex items-center space-x-6">
              <Avatar :user="authStore.user" size="xl" />
              
              <div class="flex-1">
                <div class="space-y-4">
                  <!-- Upload Button -->
                  <div>
                    <input
                      ref="fileInput"
                      type="file"
                      accept="image/jpeg,image/png,image/gif"
                      @change="handleFileSelect"
                      class="hidden"
                    />
                    <button
                      @click="$refs.fileInput.click()"
                      :disabled="uploading"
                      class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 disabled:opacity-50"
                    >
                      {{ uploading ? 'Uploading...' : 'Upload New Avatar' }}
                    </button>
                    <p class="text-xs text-gray-500 mt-1">
                      JPG, PNG, or GIF. Max 5MB.
                    </p>
                  </div>

                  <!-- Remove Button -->
                  <div v-if="authStore.user?.profile?.custom_avatar_url">
                    <button
                      @click="handleRemoveAvatar"
                      :disabled="removing"
                      class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 disabled:opacity-50"
                    >
                      {{ removing ? 'Removing...' : 'Remove Avatar' }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- User Info (Read-only) -->
          <div class="space-y-6">
            <h3 class="text-lg font-medium text-gray-900">Account Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                <input
                  :value="authStore.user?.name || ''"
                  readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                <input
                  :value="authStore.user?.email || ''"
                  readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                />
              </div>
            </div>
            
            <p class="text-sm text-gray-500">
              Account information cannot be edited at this time.
            </p>
          </div>
        </div>

        <!-- Success/Error Messages -->
        <div v-if="message" class="p-6 border-t border-gray-200">
          <div :class="[
            'p-4 rounded-md',
            messageType === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          ]">
            <p :class="[
              'text-sm',
              messageType === 'success' ? 'text-green-700' : 'text-red-700'
            ]">
              {{ message }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '../../stores/auth'
import Avatar from '../../components/common/Avatar.vue'
import api from '../../utils/axios'

const authStore = useAuthStore()
const activeTab = ref('account')
const uploading = ref(false)
const removing = ref(false)
const message = ref('')
const messageType = ref<'success' | 'error'>('success')

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  // Validate file
  if (file.size > 5 * 1024 * 1024) {
    showMessage('File size must be less than 5MB', 'error')
    return
  }
  
  if (!['image/jpeg', 'image/png', 'image/gif'].includes(file.type)) {
    showMessage('Please select a JPG, PNG, or GIF image', 'error')
    return
  }
  
  uploading.value = true
  message.value = ''
  
  try {
    const formData = new FormData()
    formData.append('avatar', file)
    
    await api.post('/profile/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    // Refresh user data to get the new Base64 avatar
    await authStore.checkAuth()
    
    showMessage('Avatar updated successfully!', 'success')
  } catch (error: any) {
    showMessage(error.response?.data?.message || 'Failed to upload avatar', 'error')
  } finally {
    uploading.value = false
    // Clear file input
    target.value = ''
  }
}

const handleRemoveAvatar = async () => {
  removing.value = true
  message.value = ''
  
  try {
    await api.delete('/profile/avatar')
    
    // Refresh user data
    await authStore.checkAuth()
    showMessage('Avatar removed successfully!', 'success')
  } catch (error: any) {
    showMessage(error.response?.data?.message || 'Failed to remove avatar', 'error')
  } finally {
    removing.value = false
  }
}

const showMessage = (text: string, type: 'success' | 'error') => {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}
</script>

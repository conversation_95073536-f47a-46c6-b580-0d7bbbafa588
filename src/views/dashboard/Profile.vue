<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-2xl mx-auto px-4">
      <div class="bg-white rounded-lg shadow-md p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <Avatar :user="authStore.user" size="xl" class="mx-auto mb-4" />
          <h1 class="text-3xl font-bold text-gray-900">{{ authStore.user?.name || 'User' }}</h1>
          <p class="text-gray-600">{{ authStore.user?.email }}</p>
        </div>

        <!-- User Info -->
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
              <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                {{ authStore.user?.name || 'Not provided' }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
              <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                {{ authStore.user?.email }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Account Type</label>
              <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                {{ accountType }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Member Since</label>
              <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                {{ joinDate }}
              </p>
            </div>
          </div>

          <!-- Edit Note -->
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
              </svg>
              <p class="text-sm text-blue-700">
                To edit your profile information, go to 
                <router-link to="/settings" class="font-medium underline hover:text-blue-800">
                  Settings → Account
                </router-link>
              </p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-center space-x-4">
            <router-link 
              to="/settings"
              class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-6 rounded-md transition-colors duration-200"
            >
              Edit Profile
            </router-link>
            <router-link 
              to="/"
              class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-md transition-colors duration-200"
            >
              Back to Home
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '../../stores/auth'
import Avatar from '../../components/common/Avatar.vue'

const authStore = useAuthStore()

const accountType = computed(() => {
  const user = authStore.user
  if (!user) return 'Unknown'
  
  // Check if OAuth user
  if (user.profile?.custom_avatar_url || user.google_id) {
    return 'Google Account'
  }
  
  return 'Email Account'
})

const joinDate = computed(() => {
  const user = authStore.user
  if (!user?.created_at) return 'Unknown'
  
  return new Date(user.created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})
</script>

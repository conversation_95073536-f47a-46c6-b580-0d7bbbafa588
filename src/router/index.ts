import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/public/Home.vue'
import About from '../views/public/About.vue'
import Login from '../views/auth/Login.vue'
import Signup from '../views/auth/Signup.vue'
import VerifyOtp from '../views/auth/VerifyOtp.vue'
import ForgotPassword from '../views/auth/ForgotPassword.vue'
import VerifyResetOtp from '../views/auth/VerifyResetOtp.vue'
import ResetPassword from '../views/auth/ResetPassword.vue'
import Profile from '../views/dashboard/Profile.vue'
import Settings from '../views/dashboard/Settings.vue'
import { requireGuest, requireAuth } from './guards'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
    // No auth required - public landing page
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    beforeEnter: requireAuth
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    beforeEnter: requireAuth
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    beforeEnter: requireGuest
  },
  {
    path: '/signup',
    name: 'Signup',
    component: Signup,
    beforeEnter: requireGuest
  },
  {
    path: '/verify-otp',
    name: 'VerifyOtp',
    component: VerifyOtp,
    beforeEnter: requireGuest
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
    beforeEnter: requireGuest
  },
  {
    path: '/verify-reset-otp',
    name: 'VerifyResetOtp',
    component: VerifyResetOtp,
    beforeEnter: requireGuest
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: ResetPassword,
    beforeEnter: requireGuest
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router

import { useAuthStore } from '../stores/auth'

export const requireAuth = async () => {
  const authStore = useAuthStore()

  // Only check if not already authenticated
  if (!authStore.isAuthenticated) {
    const isAuthenticated = await authStore.checkAuth()
    if (!isAuthenticated) {
      return '/login'
    }
  }
  return true
}

export const requireGuest = async () => {
  const authStore = useAuthStore()

  // Only check if not already determined
  if (authStore.isAuthenticated) {
    return '/'
  }
  return true
}
